from db_config.gpt_prompts_db import get_prompt_by_label
# Agent Prompts
context_prompt = get_prompt_by_label("CONTEXT_PROMPT") or None

page_classifier_prompt = get_prompt_by_label("PAGE_CLASSIFIER_PROMPT") or None

question_page_classifier_prompt = get_prompt_by_label("QUESTION_PAGE_CLASSIFIER_PROMPT") or None

explanation_page_classifier_prompt = get_prompt_by_label("EXPLANATION_PAGE_CLASSIFIER_PROMPT") or None

pattern_classifier = get_prompt_by_label("PATTERN_CLASSIFIER") or None

# Function-based prompts
def answer_key_extractor_prompt() -> str:
    return get_prompt_by_label("ANSWER_KEY_EXTRACTOR_PROMPT") or ""

def question_extractor_prompt() -> str:
    return get_prompt_by_label("QUESTION_EXTRACTOR_PROMPT") or ""

def explanation_extractor_prompt() -> str:
    return get_prompt_by_label("EXPLANATION_EXTRACTOR_PROMPT") or ""

pre_process_prompt = get_prompt_by_label("PRE_PROCESS_PROMPT") or None

pre_process_explanation_prompt = get_prompt_by_label("PRE_PROCESS_EXPLANATION_PROMPT") or None

def explanation_number_audit_prompt():
    return get_prompt_by_label("EXPLANATION_NUMBER_AUDIT_PROMPT") or ""
def explanation_recovery_prompt(missing_str: str):
    prompt = get_prompt_by_label("EXPLANATION_RECOVERY_PROMPT") or ""
    return prompt.replace("{missing_str}", missing_str) if prompt else ""

def extra_fallback_explanation_prompt():
    return get_prompt_by_label("EXTRA_FALLBACK_EXPLANATION_PROMPT") or ""

# Vision Model Prompts
def html_extractor_prompt_vision(page_number: str) -> str:
    prompt = get_prompt_by_label("HTML_EXTRACTOR_PROMPT_VISION") or ""
    return prompt.replace("{page_number}", page_number) if prompt else ""

def question_extractor_prompt_vision(page_number: str) -> str:
    prompt = get_prompt_by_label("QUESTION_EXTRACTOR_PROMPT_VISION") or ""
    return prompt.replace("{page_number}", page_number) if prompt else ""

def text_extraction_prompt(page_number: str) -> str:
    prompt = get_prompt_by_label("TEXT_EXTRACTION_PROMPT") or ""
    return prompt.replace("{page_number}", page_number) if prompt else ""

def html_content_validation_prompt(page_number: str) -> str:
    prompt = get_prompt_by_label("HTML_CONTENT_VALIDATION_VISION") or ""
    return prompt.replace("{page_number}", page_number) if prompt else ""

def extracted_question_validation_prompt(page_number: str) -> str:
    prompt = get_prompt_by_label("EXTRACTED_QUESTION_VALIDATION_VISION") or ""
    return prompt.replace("{page_number}", page_number) if prompt else ""


# Task Manager Prompts
def content_validation_prompt(content_text: str) -> str:
    prompt = get_prompt_by_label("CONTENT_VALIDATION_PROMPT") or ""
    return prompt.replace("{content_text}", content_text) if prompt else ""

def text_block_validation_prompt(content_text: str) -> str:
    prompt = get_prompt_by_label("TEXT_BLOCK_VALIDATION_PROMPT") or ""
    return prompt.replace("{content_text}", content_text) if prompt else ""

contextualize_q_system_prompt = """Given a chat history and the latest user question 
    which might reference context in the chat history, formulate a standalone question 
    which can be understood without the chat history. Do NOT answer the question,
    just reformulate it if needed and otherwise return it as is."""

rag_qa_system_prompt = """You are an AI assistant designed to help students by answering their questions based on the given chapter's context. Follow these guidelines to ensure accurate and helpful responses:
    Error Correction: If the student's question contains spelling mistakes or is not framed correctly, attempt to interpret and correct the errors before answering.
    Clarification Requests: If the question is ambiguous or unclear, ask the student for clarification while providing any possible related information from the chapter's context.
    Politeness: Always respond politely and encourage the student to ask more questions related to the chapter.
    Language Flexibility: Understand that students may ask questions in different languages or use English letters to write in another language. Respond in the same language and, if requested, explain in a different language as well.
    Chapter Navigation: If the student asks about contents from a different chapter, inform them that they can access that chapter by navigating the chapter list in the application if they have purchased the book. If not, encourage them to purchase the book to access all chapters.
    No Outside Recommendations: Do not recommend any outside books.
    PDF and Free Version Requests: If the student asks how to download a PDF or get a free version of the book, inform them that the PDF cannot be downloaded, but it can be accessed from this ebook reader at all times, benefiting from all its features. If the student asks how to print the PDF, inform them that the PDF cannot be printed.
    Markdown Formatting: Provide answers in markdown format.
    Template:
    {context}
    Instructions:
    Interpret and correct any spelling mistakes or poorly framed questions.
    Clarify any ambiguities in the student's question.
    Answer politely, sticking strictly to the given chapter's context.
    Use markdown format for the answers.
    Respond in the language the student uses, including when they write in another language using English letters. Provide explanations in different languages if requested.
    """

ext_qa_system_prompt = """
    Always assume the user's question is based on the content of the current webpage. Use this content as the primary 
    source to provide accurate answers. If the content is not related to the page, politely inform the user that the information isn't available.
Template:
{context}
Template for Responses:
- Polite tone, correcting spelling or unclear questions when necessary.
- Markdown format for better readability.
"""

mcq_exp_sys_prompt = """The following is a multiple-choice question intended for students. Please provide a clear,
                      and to the point explanation of the mcq in simple language suitable for a student.
                      - Explanation should be in the language of the question given.
                      ${prompt} 
                      Please make sure the explanation is engaging, easy to understand, and helpful for students studying this topic.
                      Do not deviate from the topic of the MCQ, in the example.
                      Do not include images in the response
                      Use markdown format for the answers.
                      """

mcq_qa_sys_prompt = """Create a set of similar multiple-choice questions (MCQs) based on the following example's topic, strictly following these rules:
                        
                        ### **Rules:**
                        1. Each question should be clear, well-structured, and suitable for students.
                        2. **Do not deviate from the topic** of the given example MCQ.
                        3. If generating any similar MCQ **requires an image for the question to make sense**, then **STRICTLY DO NOT** generate that question.
                        4. **DO NOT include any placeholders** such as:
                           - "Question Figures:"
                           - "Answer Figures:"
                           - "[Image here]"
                           - Any similar text-based references to images.
                        5. **If you cannot generate a valid MCQ without an image, return ONLY this exact text:**
                           `"Not able to generate similar MCQ"`
                           **Do not generate anything else.**
                        6. **Every MCQ must be self-contained, readable, and fully understandable without any missing information.**
                        7. Format the options as `A.`, `B.`, `C.`, `D.` and ensure that each question is **bold**.
                        8. Include only **one correct answer** per question.
                        9. Use **Markdown format** for the answers.
                        10. **If you break any of these rules, you are violating the instructions. Follow them exactly.**
                        
                        Now, generate the MCQs.
                        
                        ${prompt}
                      """


hint_qa_sys_prompt = """
                      - The following is a multiple-choice question intended for students. Please provide a clear,
                        hint for the mcq in simple language suitable for a student.
                      - Hint should be in the language of the question given.
                      ${prompt} 
                      Please make sure the hint is engaging, easy to understand, and helpful for students studying this topic.
                      Do not deviate from the topic of the MCQ, in the example.
                      Do not include images in the response
                      Use markdown format for the answers.
                    """

qa_explanation = """
                    The following is a Question and Answer intended for students. Please provide a DETAILED and
                    clear explanation in simple language suitable for a student.
                    - Explanation should be in the language of the question given.
                    Please make sure the explanation is DETAILED, engaging, easy to understand, and helpful for students studying this topic.
                    Do not deviate from the topic of the MCQ, in the example.
                    Do not include images in the response
                    Use markdown format for the answers.
                """
pdf_ocr_prompt = """
    You are an expert OCR and document layout model. Your task is to extract the content from the given image.
    - CRITICAL RULE:
        - **Do not infer, create, guess, generate, or assume any missing content.**
    Your instructions:
    - Extract the content exactly as seen in the image.
    - Include all parts of the contents.
    - Include all the headings and sub-headings.
    - Describe the diagram/image
    - Mention all labels and visual cues as printed.
    
    IMPORTANT RULES FOR MATH FORMAT (STRICT)
    • Every mathematical expression MUST be wrapped:
       - Inline math: \\( ... \\)
       - Block math: \\[ ... \\] or $$ ... $$
    • Inside JSON or quoted strings, escape every backslash:
       - Correct: "\\( \\frac{a}{b} \\)" or \\(\\times\\)
       - Wrong:   "\frac{a}{b}"  or  "\\frac{a}{b}"
    • Never output raw LaTeX without wrappers. Always validate that any math expression is inside \\( ... \\) or \\[ ... \\].
    • Never emit a LaTeX command like `\frac`, `\sin`, etc., unless inside a wrapped block.
Output only text, no comments
- Do NOT use any markdown headings.
"""